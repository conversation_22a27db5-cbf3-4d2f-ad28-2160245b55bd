/**
 * TUTOR LMS ÖZEL CSS DOSYASI
 * Bu dosya Tutor LMS eklentisinin görünümünü özelleştirmek ve kullanıcı deneyimini iyileştirmek için kullanılır.
 * Mobil ve masaüstü cihazlarda optimum performans ve görünüm sağlamak için tasarlanmıştır.
 *
 * İÇERİK TABLOSU:
 * 1. Temel Değişkenler ve Genel Stiller - <PERSON>k tanımları, değişkenler ve temel sayfa düzeni
 * 2. Buton Stilleri - Tüm butonların görünüm, davran<PERSON>ş ve etkileşimleri
 * 3. Sidebar Stilleri - Yan menü tasarımı, içerik organizasyonu ve navigasyon
 * 4. İçerik Alanı Stilleri - Ana içerik bölümünün düzeni ve görsel yapısı
 * 5. Başlık ve Navigasyon Stilleri - <PERSON>st men<PERSON>, alt menü ve gezinme elemanları
 * 6. Video Oynatıcı Stilleri - Video oynatıcı görünümü, kontrolleri ve etkileşimleri
 * 7. Sekme ve Tab Stilleri - İçerik sekmelerinin görünümü ve davranışları
 * 8. Animasyon ve Efektler - Geçiş animasyonları, yükleme efektleri ve görsel geri bildirimler
 * 9. Responsive Tasarım Ayarları - Farklı ekran boyutları için optimize edilmiş düzenlemeler
 * 10. Performans Optimizasyonları - Sayfa yükleme hızını ve kullanıcı deneyimini iyileştiren düzenlemeler
 * 11. Yorumlar Modülü Stilleri - Yorum sistemi için özel tasarımlar ve etkileşim özellikleri
 * 12. Hamburger Menü Hover Efekti - Menü açma/kapama butonları için görsel geri bildirimler
 * 13. Form Elemanları Stilleri - Giriş alanları, onay kutuları ve diğer form elemanları
 *
 * SEO ve Performans Optimizasyonu:
 * - Tüm CSS seçicileri performans için optimize edilmiştir
 * - Mobil öncelikli tasarım yaklaşımı benimsenmiştir
 * - Sayfa yükleme hızını artırmak için gereksiz animasyonlar kaldırılmıştır
 * - Kullanıcı deneyimini iyileştirmek için görsel ipuçları eklenmiştir
 */

/* ===============================================================
   1. TEMEL DEĞİŞKENLER VE GENEL STİLLER
   =============================================================== */

/**
 * CSS Değişkenleri - Renk ve Tema Ayarları
 * Bu değişkenler tüm tema boyunca tutarlı renk ve stil kullanımı sağlar.
 * Ana renk değişkenleri admin panelinden ayarlanır ve :root'a eklenir.
 */
:root {
    /* Türetilmiş renk değişkenleri - Ana renk değişkenlerinden hesaplanır */
    --tutor-light-primary: color-mix(in srgb, var(--tutor-color-primary) 5%, transparent); /* %5 opaklıkta ana renk */
    --tutor-medium-primary: color-mix(in srgb, var(--tutor-color-primary) 10%, transparent); /* %10 opaklıkta ana renk */
    --tutor-primary-lighter: color-mix(in srgb, var(--tutor-color-primary) 15%, transparent); /* %15 opaklıkta ana renk - hover için */

    /* Başarı renkleri */
    --tutor-success-color: #119401; /* Başarı işlemleri için yeşil renk */
    --tutor-success-hover: #0d7001; /* Başarı butonları hover durumu */

    /* İkincil metin rengi */
    --tutor-light-text: #757C8E; /* İkincil metin rengi - açık gri */

    /* Mobil menü renkleri */
    --tutor-mobile-header-bg: var(--tutor-color-primary); /* Mobil görünümde alt menü arka plan rengi */
    --tutor-mobile-text: #ffffff; /* Mobil görünümde metin rengi */
    --tutor-mobile-border: #ffffff52; /* Mobil görünümde buton kenarlık rengi */
}

/**
 * Sayfa Arka Plan Rengi
 * Sadece kurs görüntüleme sayfalarında hafif mavi arka plan kullanılır.
 * Bu, içeriğin daha rahat okunmasını sağlar ve kullanıcı deneyimini iyileştirir.
 * Dark mode'da koyu arka plan rengi kullanılır.
 */
body.tutor-course-viewing-page {
    background-color: var(--tutor-light-primary) !important;
}

/* Dark mode için kurs izleme ekranı arka plan rengi */
html[data-theme="dark"] body.tutor-course-viewing-page,
body.tutor-dark-mode.tutor-course-viewing-page {
    background-color: #0F0F0F !important;
}

/* Daha yüksek öncelikli dark mode arka plan rengi */
html[data-theme="dark"] body.tutor-course-viewing-page,
body.tutor-dark-mode.tutor-course-viewing-page,
html[data-theme="dark"] .tutor-course-viewing-page,
.tutor-dark-mode.tutor-course-viewing-page,
html[data-theme="dark"] .tutor-course-single-content-wrapper,
.tutor-dark-mode .tutor-course-single-content-wrapper {
    background-color: #0F0F0F !important;
}


/* ===============================================================
   2. BUTON STİLLERİ
   =============================================================== */

/**
 * Genel Buton Ayarları
 * Tüm butonlar için temel boyut, yazı tipi ve görünüm ayarları.
 * Tutarlı bir kullanıcı arayüzü için standart buton stilleri tanımlanmıştır.
 */
.tutor-btn {
    padding: 7px 16px !important; /* Standart iç boşluk */
    font-size: 14px !important; /* Okunabilir yazı boyutu */
    transition: none !important; /* Geçiş efekti kaldırıldı */
}

/**
 * Başarı Butonu - Yeşil
 * Tamamlama, onaylama gibi olumlu işlemler için kullanılır.
 * Kullanıcıya olumlu bir eylem gerçekleştirdiğini gösterir.
 */
.tutor-btn-success {
    background-color: var(--tutor-success-color) !important; /* Yeşil arka plan */
    color: #ffffff !important; /* Beyaz metin */
    border-color: var(--tutor-success-color) !important; /* Yeşil kenarlık */
}

/* Başarı butonu hover durumu */
.tutor-btn-success:hover {
    background-color: var(--tutor-success-hover) !important; /* Daha koyu yeşil */
    border-color: var(--tutor-success-hover) !important;
}

/**
 * İkincil Buton
 * Alternatif işlemler ve ikincil öncelikli butonlar için.
 * Ana butondan sonra ikinci derecede önemli eylemleri temsil eder.
 */
.tutor-btn-secondary {
    color: var(--tutor-color-primary) !important; /* Mavi metin */
    background-color: var(--tutor-medium-primary) !important; /* Hafif mavi arka plan */
}

/* İkincil buton hover durumu */
.tutor-btn-secondary:hover {
    background-color: var(--tutor-color-primary) !important; /* Tam mavi arka plan */
    color: #ffffff !important; /* Beyaz metin */
}

/* Secondary Button Styling - Masaüstü için tekrar tanımlama */
.tutor-btn-secondary {
    color: var(--tutor-color-primary) !important;
    background-color: var(--tutor-medium-primary) !important;
}

/* Secondary Button Hover Styling */
.tutor-btn-secondary:hover {
    background-color: var(--tutor-color-primary) !important;
    color: #ffffff !important;
}

/**
 * Mobil Görünüm Buton Ayarları
 * Tablet ve mobil cihazlarda butonların görünümü.
 * Daha küçük ekranlarda daha kompakt ve dokunmatik kullanıma uygun tasarım.
 */
@media (max-width: 1199px) {
    /* Mobil görünümde ikincil buton */
    .tutor-btn-secondary {
        color: var(--tutor-mobile-text) !important; /* Beyaz metin */
        background-color: transparent !important; /* Saydam arka plan */
        border-color: var(--tutor-mobile-border) !important; /* Yarı saydam beyaz kenarlık */
    }

    /* Butonların daha kompakt görünmesi için */
    .tutor-course-topic-single-header .tutor-btn {
        padding: 5px 8px !important; /* Daha az iç boşluk */
        min-width: auto !important; /* Minimum genişlik sınırı kaldırıldı */
        height: 36px !important; /* Sabit yükseklik */
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
    }
}

/**
 * Gizlenen Butonlar
 * Kullanılmayan veya gereksiz butonları gizler.
 * Kullanıcı arayüzünü sadeleştirmek ve karmaşıklığı azaltmak için.
 */
.tutor-custom-fullscreen-btn {
    display: none !important; /* Tam ekran butonu gizlendi */
}

/* İlk ve son dersler için navigasyon butonlarını gizleme */
/* İlk ders için önceki butonu gizle */
.tutor-single-course-content-prev a[disabled="disabled"],
.tutor-single-course-content-next a[disabled="disabled"] {
    display: none !important;
}

/* Butonun kapsayıcı div'ini de gizle (eğer içinde başka bir şey yoksa) */
.tutor-single-course-content-prev:has(a[disabled="disabled"]),
.tutor-single-course-content-next:has(a[disabled="disabled"]) {
    display: none !important;
}

/* Alternatif çözüm - modern tarayıcılar için :has desteği yoksa */
.tutor-single-course-content-prev a[disabled="disabled"] + .tutor-single-course-content-prev,
.tutor-single-course-content-next a[disabled="disabled"] + .tutor-single-course-content-next {
    display: none !important;
}


/* ===============================================================
   3. SIDEBAR STİLLERİ
   =============================================================== */

/**
 * Sidebar Temel Ayarları
 * Yan menünün temel görünüm ve davranış özellikleri.
 * Kullanıcıların kurs içeriğinde kolayca gezinmesini sağlar.
 */
html body .tutor-course-single-sidebar-wrapper {
    padding-top: 0px !important;
    overflow-x: hidden !important; /* Yatay kaydırma çubuğunu gizle */
    background-color: #f5f7fd; /* Yan sütun arka plan rengi */
    border-right: none !important; /* Sağ kenarlığı kaldır */
}

/* Dark mode için sidebar arka plan rengi */
html[data-theme="dark"] body .tutor-course-single-sidebar-wrapper,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper {
    background-color: #121212 !important;
}

/**
 * Sidebar Başlık Stili
 * Yan menünün üst kısmında yer alan başlık bölümü.
 * Kurs başlığını ve diğer önemli bilgileri gösterir.
 */
.tutor-course-single-sidebar-title {
    background-color: #f5f7fd !important;
    border-bottom: none !important;
    padding: 8px 8px !important;
}

/* Dark mode için sidebar başlık arka plan rengi */
html[data-theme="dark"] .tutor-course-single-sidebar-title,
body.tutor-dark-mode .tutor-course-single-sidebar-title {
    background-color: #121212 !important;
}

/**
 * Web Görünümünde Arama Kutusu Sticky Pozisyonlama
 * Masaüstü görünümünde arama kutusunu sabit (sticky) yapar.
 * Kullanıcının içerik listesinde gezinirken arama kutusuna kolay erişimini sağlar.
 */
@media (min-width: 1200px) {
    .tutor-course-single-sidebar-title {
        position: sticky !important;
        top: 0 !important;
        z-index: 99 !important;
    }
}

/**
 * Admin Bar Varsa Sidebar Başlığı İçin Margin
 * WordPress admin çubuğu varken sidebar başlığının doğru konumlandırılması.
 */
body.admin-bar .tutor-course-single-sidebar-title {
    margin-top: 30px !important;
}

/* Admin bar varsa sticky arama kutusu için top değeri ayarla */
@media (min-width: 1200px) {
    body.admin-bar .tutor-course-single-sidebar-title {
        top: 32px !important; /* WordPress admin bar yüksekliği */
    }
}

/* Tablet ve mobil cihazlarda admin bar için farklı margin */
@media (max-width: 1199px) {
    body.admin-bar .tutor-course-single-sidebar-title {
        margin-top: 50px !important;
    }
}

/**
 * Sidebar Accordion Stilleri
 * Kurs içeriğinin bölümler halinde gösterildiği akordeon menü.
 * Kullanıcıların içeriği kategorilere göre görüntülemesini sağlar.
 */
.tutor-course-single-sidebar-wrapper .tutor-course-topic {
    padding: 10px 10px 0px 10px !important;
}
.tutor-course-single-sidebar-wrapper .tutor-accordion-item-body {
    background-color: #ffffff !important;
    padding-top: 0px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
    border-radius: 0 0 8px 8px !important; /* Sadece alt köşeleri yuvarlatma */
    margin-bottom: 8px !important;
}

.tutor-course-single-sidebar-wrapper .tutor-accordion-item-header {
    border-bottom-color: transparent !important; /* Kapalıyken kenarlık gizli */
    border-bottom-style: dashed !important;
    background-color: #ffffff !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
    border-radius: 8px !important; /* Kapalıyken tüm köşeler yuvarlak */
    margin-bottom: 0 !important; /* Gövde ile arasında boşluk olmasın */
    transition: box-shadow 0.3s ease, border-radius 0.3s ease, border-bottom-color 0.3s ease !important;
}

/* Açık durumdaki akordiyonlar için köşe yuvarlatma */
.tutor-course-single-sidebar-wrapper .tutor-accordion-item-header.is-active {
    border-bottom-color: transparent !important; /* Açıkken de kenarlık gizli */
    border-radius: 8px 8px 0 0 !important; /* Sadece üst köşeleri yuvarlatma */
}

/* Dark mode için accordion stilleri */
html[data-theme="dark"] .tutor-course-single-sidebar-wrapper .tutor-accordion-item-body,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper .tutor-accordion-item-body {
    background-color: #1E1E1E !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

html[data-theme="dark"] .tutor-course-single-sidebar-wrapper .tutor-accordion-item-header,
body.tutor-dark-mode .tutor-course-single-sidebar-wrapper .tutor-accordion-item-header {
    background-color: #1E1E1E !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

/* Hover durumunda box-shadow kaldırıldı */

/**
 * Accordion içindeki tamamlanma sayıları için stil
 * Kullanıcının her bölümdeki ilerleme durumunu gösterir.
 * Görsel olarak belirgin ve kolay anlaşılır bir tasarım.
 */
.tutor-course-topic-summary-top {
    flex-wrap: wrap !important;
    border-radius: 10px !important;
    padding: 10px !important;
}

.tutor-course-topic-summary-top .tutor-course-topic-summary {
    font-size: 13px !important;
    font-weight: 600 !important;
    color: var(--tutor-color-primary) !important; /* Değişken kullanımı */
    background-color: var(--tutor-medium-primary) !important; /* Değişken kullanımı */
    padding: 4px 10px !important;
    border-radius: 10px !important;
    display: inline-block !important;
    margin-left: 5px !important;
    max-width: calc(100% - 10px) !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
}

/**
 * Accordion içindeki açıklama metni için stil
 * Her bölümün kısa açıklamasını gösterir.
 * Kullanıcıya içerik hakkında ön bilgi verir.
 */
.tutor-course-topic-description {
    font-size: 13px !important;
    color: #3C4C5A !important;
    line-height: 1.4 !important;
    width: 100% !important;
    margin-bottom: 8px !important;
    padding-left: 8px !important;
    padding-right: 8px !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    white-space: normal !important;
}

/* Dark mode için açıklama metni rengi */
html[data-theme="dark"] .tutor-course-topic-description,
body.tutor-dark-mode .tutor-course-topic-description {
    color: #BBBBBB !important;
}

/**
 * Accordion başlık satırı için flex-wrap özelliği
 * Hem açık hem kapalı durumlar için tutarlı görünüm sağlar.
 * Başlık ve içerik öğelerinin düzgün hizalanmasını sağlar.
 */
#tutor-page-wrap > div.tutor-course-single-content-wrapper.tutor-spotlight-mode > div.tutor-course-single-sidebar-wrapper.tutor-lesson-sidebar > div.tutor-course-topic > div.tutor-accordion-item-header.is-active > div,
#tutor-page-wrap > div.tutor-course-single-content-wrapper.tutor-spotlight-mode.tutor-course-single-sidebar-open > div.tutor-course-single-sidebar-wrapper.tutor-lesson-sidebar.tutor-lesson-sidebar-show > div.tutor-course-topic > div.tutor-accordion-item-header > div,
#tutor-page-wrap > div.tutor-course-single-content-wrapper.tutor-spotlight-mode > div.tutor-course-single-sidebar-wrapper.tutor-lesson-sidebar > div.tutor-course-topic > div.tutor-accordion-item-header > div {
    flex-wrap: nowrap !important;
}

/**
 * Kurs Konu Öğeleri ve Etkileşim Stilleri
 * Sidebar içindeki ders ve içerik bağlantılarının görünümü.
 * Kullanıcının şu anda nerede olduğunu ve hangi içerikleri tamamladığını gösterir.
 */

/**
 * Hover efekti sadece masaüstü için
 * Fare ile üzerine gelindiğinde görsel geri bildirim sağlar.
 * Kullanıcıya tıklanabilir öğeleri belirtir.
 */
@media (min-width: 1200px) {
    .tutor-course-single-sidebar-wrapper .tutor-course-topic-item a:hover {
        background-color: var(--tutor-light-primary) !important;
        transition: none !important;
    }
}

/**
 * Aktif Konu Öğesi
 * Kullanıcının şu anda görüntülediği içeriği belirtir.
 * Görsel olarak diğer öğelerden ayırt edilebilir.
 */
.tutor-course-single-sidebar-wrapper .tutor-course-topic-item.is-active::before {
    content: none; /* Varsayılan işaretçiyi kaldır */
}

.tutor-course-single-sidebar-wrapper .tutor-course-topic-item.is-active .tutor-course-topic-item-icon {
    opacity: 1 !important; /* İkonu tekrar görünür yap */
}

.tutor-course-single-sidebar-wrapper .tutor-course-topic-item.is-active a {
    background-color: color-mix(in srgb, var(--tutor-color-primary) 20%, transparent) !important;
}

/**
 * Sidebar Kurs Başlığı
 * Yan menünün üst kısmında görünen kurs başlığı.
 * Kullanıcıya hangi kursta olduğunu hatırlatır.
 */
html body .desktop-course-title {
    font-size: 16px !important;
    font-weight: 600 !important;
    color: var(--tutor-text-color) !important;
    margin-bottom: 12px !important;
    padding-bottom: 0px !important;
    word-break: break-word !important;
    overflow-wrap: break-word !important;
    hyphens: auto !important;
}

/* Mobil cihazlarda başlık için özel stil */
@media (max-width: 767px) {
    html body .desktop-course-title {
        margin-right: 30px !important; /* Kapatma butonuna yer açmak için */
    }
}

/**
 * Sidebar Kapatma Butonu
 * Yan menüyü kapatmak için kullanılan buton.
 * Kullanıcının içerik alanını genişletmesini sağlar.
 */
a[tutor-hide-course-single-sidebar] {
    position: absolute !important;
    top: -100px !important;
    right: 15px !important;
    z-index: 999 !important;
    cursor: pointer !important;
}

/**
 * Çarpı İkonu Stilleri
 * Kapatma butonunda kullanılan çarpı ikonu.
 * Kullanıcıya kapatma işlevini görsel olarak belirtir.
 */
.tutor-course-single-sidebar-wrapper .tutor-icon-times {
    position: relative !important;
    transition: transform 0.2s ease !important; /* Tıklama animasyonu için */
}

/* Hover durumunda hafif döndürme efekti */
a[tutor-hide-course-single-sidebar]:hover .tutor-icon-times {
    transform: rotate(90deg) !important;
}

/**
 * Sidebar Arama Kutusu
 * Kurs içeriğinde arama yapmak için kullanılan giriş alanı.
 * Kullanıcıların büyük kurslar içinde belirli içerikleri hızlıca bulmasını sağlar.
 */
#tutor-course-content-search {
    width: 100%;
    font-size: 14px;
    border-radius: 6px;
    border: 1px solid var(--tutor-border-color);
    padding: 8px 12px;
    padding-right: 35px; /* Arama ikonu için sağ tarafta boşluk */
    background-color: #ffffff !important;
    transition: all 0.2s ease !important; /* Geçiş efekti */
}

/* Dark mode için arama kutusu stilleri */
html[data-theme="dark"] #tutor-course-content-search,
body.tutor-dark-mode #tutor-course-content-search {
    background-color: #2A2A2A !important;
    border-color: #3A3A3A !important;
    color: #F5F5F5 !important;
}

/**
 * Arama sırasında gereksiz içerikleri gizleme
 * Arama sonuçlarına odaklanmak için diğer içerikleri gizler.
 * Kullanıcı deneyimini iyileştirir ve arama sonuçlarını daha belirgin hale getirir.
 */
.tutor-course-single-sidebar-wrapper.tutor-searching .tutor-course-topic-description,
.tutor-course-single-sidebar-wrapper.tutor-searching .tutor-course-topic-summary-top {
    display: none !important;
}

/**
 * Arama sonuçları bulunamadı mesajı
 * Arama sonuçları bulunamadığında kullanıcıya bilgi verir.
 */
.tutor-course-single-sidebar-wrapper.tutor-searching.tutor-search-no-results::after {
    content: "Sonuç bulunamadı";
    display: block;
    text-align: center;
    padding: 20px;
    font-size: 14px;
    color: #333;
    background-color: #f5f7fd;
    margin: 10px;
    border-radius: 6px;
}

/**
 * Arama kutusuna odaklanıldığında stil değişiklikleri
 * Kullanıcıya aktif giriş alanını görsel olarak belirtir.
 * Kullanıcı deneyimini iyileştirir ve geri bildirim sağlar.
 */
#tutor-course-content-search:focus {
    border-color: var(--tutor-color-primary);
    box-shadow: 0 0 0 2px color-mix(in srgb, var(--tutor-color-primary) 15%, transparent);
    outline: none;
}

/* Odaklanıldığında arama ikonunun rengini değiştir */
#tutor-course-content-search:focus + .tutor-search-icon,
#tutor-course-content-search:focus ~ #tutor-course-search-icon {
    color: var(--tutor-color-primary) !important;
}

/**
 * Arama İkonu Stilleri
 * Arama kutusunun sağ tarafında görünen ikon.
 * Kullanıcıya arama işlevini görsel olarak belirtir.
 */
.tutor-search-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--tutor-light-text);
    cursor: pointer;
    transition: color 0.2s ease; /* Renk değişimi için animasyon */
}

/**
 * Arama Kutusundaki Çarpı İkonu
 * Arama kutusunu temizlemek için kullanılan ikon.
 * Kullanıcının arama sorgusunu hızlıca silmesini sağlar.
 */
.tutor-search-icon.tutor-icon-times {
    color: var(--tutor-color-primary) !important;
    position: absolute !important;
    right: 12px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    cursor: pointer !important;
    z-index: 999 !important;
    transition: transform 0.2s ease !important; /* Tıklama animasyonu */
}

/**
 * Arama kutusu için form kontrol wrapper'daki çarpı ikonu
 * Farklı form yapılarında kullanılan temizleme ikonu.
 * Tutarlı bir kullanıcı deneyimi sağlar.
 */
.tutor-form-control-wrapper .tutor-icon-times {
    position: absolute !important;
    right: 12px !important; /* Arama ikonu ile aynı konumda olması için */
    top: 50% !important; /* Dikey olarak ortalamak için */
    transform: translateY(-50%) !important; /* Tam ortalama için */
    z-index: 999 !important;
    transition: transform 0.2s ease !important; /* Tıklama animasyonu */
}

/* Hover durumunda hafif döndürme efekti */
.tutor-search-icon.tutor-icon-times:hover,
.tutor-form-control-wrapper .tutor-icon-times:hover {
    transform: translateY(-50%) rotate(90deg) !important;
}

/**
 * Kurs arama ikonu - özel ID ile
 * Kurs içeriğinde arama yapmak için kullanılan ikon.
 * Kullanıcıya arama işlevini görsel olarak belirtir.
 */
#tutor-course-search-icon {
    position: absolute !important;
    right: 12px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    color: var(--tutor-light-text) !important;
    cursor: pointer !important;
    z-index: 999 !important;
    transition: none !important; /* Animasyonlar kaldırıldı */
}

/* Hover efekti kaldırıldı */
#tutor-course-search-icon:hover {
    transform: translateY(-50%) !important;
}

/**
 * Kurs İçerik Başlıklarını Sınırlandırma
 * Uzun başlıkların düzgün görüntülenmesi için genişlik sınırlamaları.
 * Farklı ekran boyutlarında optimum görünüm sağlar.
 */

/* Masaüstü için başlık genişlikleri */
@media (min-width: 1200px) {
    .tutor-course-topic-item-title {
        max-width: 350px !important; /* Sidebar genişliği arttığı için başlık genişliği de arttırıldı */
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        display: inline-block !important;
    }

    .tutor-course-single-sidebar-wrapper .tutor-course-topic-item-title {
        max-width: 250px !important; /* Topic başlıkları için 250px */
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        display: inline-block !important;
    }
}

/* Tablet için başlık genişlikleri */
@media (max-width: 1199px) {
    .tutor-course-single-sidebar-wrapper .tutor-course-topic-item-title {
        max-width: calc(1030px - (1199px - 100vw)) !important; /* 1199px ekranda 1030px, ekran küçüldükçe aynı oranda azalır */
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        display: inline-block !important;
    }
}

/* Mobil için başlık genişlikleri */
@media (max-width: 767px) {
    .tutor-course-topic-item-title {
        max-width: 150px !important; /* Mobil ekranlarda daha küçük genişlik */
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        display: inline-block !important;
    }
}

/**
 * Sidebar Accordion Başlık Genişliği
 * Accordion başlıklarının farklı ekran boyutlarında düzgün görüntülenmesi.
 * Uzun başlıkların kırpılarak üç nokta (...) ile gösterilmesini sağlar.
 */

/* Masaüstü için accordion başlık genişliği */
@media (min-width: 1200px) {
    .tutor-course-single-sidebar-wrapper .tutor-accordion-item-header .tutor-course-topic-title {
        max-width: 350px !important; /* Geniş ekranlarda daha fazla içerik göster */
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        display: block !important;
    }
}

/* Tablet ve mobil için accordion başlık genişliği */
@media (max-width: 1199px) {
    .tutor-course-single-sidebar-wrapper .tutor-accordion-item-header .tutor-course-topic-title {
        max-width: calc(1110px - (1199px - 100vw)) !important; /* 1199px ekranda 1110px, ekran küçüldükçe aynı oranda azalır */
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        display: inline-block !important;
    }
}

/**
 * Sidebar Görsel Optimizasyonları
 * Kurs içerik öğelerinin yanında görünen küçük görseller.
 * Performans ve görünüm için optimize edilmiştir.
 */

/* Optimize edilmiş küçük resimler */
.tutor-course-topic-item-thumbnail {
    width: 65px !important;
    height: 45px !important;
    border-radius: 4px !important;
    object-fit: cover !important; /* Görsel oranını koruyarak kırpma */
    margin-right: 8px !important;
    vertical-align: middle !important;
    /* loading: lazy is added via JavaScript instead */
}

/**
 * Optimize Gradient Placeholder
 * Görsel yüklenene kadar veya görsel olmadığında gösterilen yer tutucu.
 * Kullanıcıya görsel içerik olduğunu belirtir.
 */
.tutor-course-topic-item-gradient {
    width: 65px !important;
    height: 45px !important;
    border-radius: 4px !important;
    margin-right: 8px !important;
    background: var(--tutor-color-primary) !important; /* Değişken kullanımı */
    display: inline-block !important;
    position: relative !important;
}

/**
 * Basitleştirilmiş Gradient İkonu
 * Yer tutucu içinde görüntülenen resim ikonu.
 * Performans için optimize edilmiş SVG kullanır.
 */
.tutor-course-topic-item-gradient::after {
    content: '';
    position: absolute !important;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 24px !important;
    height: 24px !important;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgba(255,255,255,0.9)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='18' height='18' rx='2'%3E%3C/rect%3E%3Ccircle cx='8.5' cy='8.5' r='1.5'%3E%3C/circle%3E%3Cpolyline points='21 15 16 10 5 21'%3E%3C/polyline%3E%3C/svg%3E") !important;
    background-size: contain !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
}


/* ===============================================================
   4. İÇERİK ALANI STİLLERİ
   =============================================================== */

/* Ana içerik alanı için dark mode stilleri */
html[data-theme="dark"] #tutor-single-entry-content,
body.tutor-dark-mode #tutor-single-entry-content {
    background-color: #0F0F0F !important;
    color: #F5F5F5 !important;
}

/* Dark mode için içerik başlıkları */
html[data-theme="dark"] #tutor-single-entry-content h1,
html[data-theme="dark"] #tutor-single-entry-content h2,
html[data-theme="dark"] #tutor-single-entry-content h3,
html[data-theme="dark"] #tutor-single-entry-content h4,
html[data-theme="dark"] #tutor-single-entry-content h5,
html[data-theme="dark"] #tutor-single-entry-content h6,
body.tutor-dark-mode #tutor-single-entry-content h1,
body.tutor-dark-mode #tutor-single-entry-content h2,
body.tutor-dark-mode #tutor-single-entry-content h3,
body.tutor-dark-mode #tutor-single-entry-content h4,
body.tutor-dark-mode #tutor-single-entry-content h5,
body.tutor-dark-mode #tutor-single-entry-content h6 {
    color: #F5F5F5 !important;
}

/**
 * İçerik Alanı Temel Stilleri
 * Kurs içeriğinin görüntülendiği ana bölüm.
 * Kullanıcının ders içeriğini rahatça görüntülemesini sağlar.
 */

/**
 * Mobil Görünümde İçerik Alanı İçin Düzenlemeler
 * Tablet ve mobil cihazlarda içerik alanının optimize edilmesi.
 * Küçük ekranlarda daha iyi bir kullanıcı deneyimi sağlar.
 */
@media (max-width: 1199px) {
    /* Butonların daha kompakt görünmesi için */
    .tutor-course-topic-single-header .tutor-btn {
        padding: 5px 8px !important;
        min-width: auto !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* İçerik metinleri için kenar boşlukları ve kelime sarma */
    .tutor-course-single-content-wrapper .tutor-container,
    .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-mt-44,
    .tutor-course-single-content-wrapper #tutor-single-entry-content h3,
    .tutor-course-single-content-wrapper #tutor-single-entry-content p {
        padding-left: 15px !important;
        padding-right: 15px !important;
        box-sizing: border-box !important;
        max-width: 100% !important;
        word-wrap: break-word !important;
    }

    /**
     * Ders Hakkında Bölümü
     * Ders açıklamasının görüntülendiği alan.
     * Alt menü ile çakışmaması için alt boşluk eklenir.
     */
    #tutor-single-entry-content .tutor-container h3,
    #tutor-single-entry-content .tutor-container p {
        padding-left: 15px !important;
        padding-right: 15px !important;
        word-break: break-word !important;
        padding-bottom: 50px !important; /* Alt menü ile çakışmaması için */
    }
}

/**
 * Kurs Konu Alt Bölümü Düzenlemeleri
 * Sayfa alt kısmındaki footer alanı.
 * Daha temiz bir görünüm için kenarlık ve arka plan kaldırıldı.
 */
.tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-footer {
    border-top: none !important;
    background-color: transparent !important;
}


/* ===============================================================
   5. BAŞLIK VE NAVİGASYON STİLLERİ
   =============================================================== */

/**
 * Başlık ve Navigasyon Bölümü
 * Kurs içeriğinin üst kısmında yer alan başlık ve gezinme alanı.
 * Kullanıcının kurs içinde gezinmesini ve ilerlemesini takip etmesini sağlar.
 */

/**
 * Kurs Konu Başlığı - Temel Ayarlar
 * Masaüstü görünümünde sayfa üst kısmındaki başlık alanı.
 * Kullanıcıya hangi içeriği görüntülediğini gösterir.
 */
html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header {
    background-color: transparent !important; /* Saydam arka plan */
    color: #000000 !important; /* Siyah metin rengi */
    padding: 8px 16px !important; /* Standart iç boşluk */
    transition: all 0.3s ease !important; /* Geçiş efekti */
}

/* Masaüstü için başlık alanı kenar boşlukları */
@media (min-width: 768px) {
    html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header {
        padding: 8px 60px !important; /* Daha geniş iç boşluk */
    }
}

/**
 * Mobil ve Tablet İçin Başlık Stilleri
 * Küçük ekranlarda sayfanın alt kısmında sabit olarak görünen başlık alanı.
 * Kullanıcının içerik arasında gezinmesini kolaylaştırır.
 */
@media (max-width: 1199px) {
    html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header {
        background-color: var(--tutor-mobile-header-bg) !important; /* Değişken kullanımı */
        padding: 8px 15px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: flex-start !important; /* Soldan başlat */
        flex-wrap: nowrap !important;
        position: fixed !important;
        z-index: 1027 !important;
        bottom: 0px !important; /* Sayfanın altında sabit pozisyon */
        width: 100% !important; /* Tam genişlik */
        left: 0 !important;
        right: 0 !important;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important; /* Üst gölge efekti */
    }

    /**
     * Mobil Görünümde Başlık Genişliği
     * Küçük ekranlarda başlık metninin sınırlandırılması.
     * Uzun başlıkların düzgün görüntülenmesini sağlar.
     */
    html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header-title {
        max-width: 170px !important; /* Mobil için sınırlı genişlik */
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        color: var(--tutor-mobile-text) !important; /* Beyaz metin rengi */
    }

    /**
     * Çarpı İkonu Stilleri
     * Sayfayı kapatmak için kullanılan ikon.
     * Kullanıcıya çıkış işlevini görsel olarak belirtir.
     */
    html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-ml-auto .tutor-icon-times {
        font-size: 16px !important;
        color: var(--tutor-mobile-text) !important; /* Değişken kullanımı */
        transition: transform 0.2s ease !important; /* Tıklama animasyonu */
    }

    /**
     * Mobil Navigasyon Butonları Düzeni
     * Alt menüdeki butonların hizalanması ve görünümü.
     * Kompakt ve kullanımı kolay bir arayüz sağlar.
     */
    .tutor-course-topic-single-header .tutor-ml-auto {
        width: auto !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        gap: 5px !important; /* Butonlar arası boşluk */
        margin-top: 0 !important;
        flex-wrap: nowrap !important;
    }

    /* Önceki/Sonraki butonlar için kenar boşlukları */
    .tutor-course-topic-single-header .tutor-single-course-content-prev,
    .tutor-course-topic-single-header .tutor-single-course-content-next {
        margin: 0 2px !important;
    }
}

/**
 * Tamamlama Butonu Stilleri
 * İçeriği tamamlandı olarak işaretlemek için kullanılan buton.
 * Kullanıcının ilerleme durumunu güncellemesini sağlar.
 */
@media (max-width: 1199px) {
    /* Mark as complete butonun boyutunu ve görünümünü düzenleme */
    .tutor-course-topic-single-header .tutor-topbar-mark-btn,
    #tutor-single-entry-content > div.tutor-course-topic-single-header.tutor-single-page-top-bar > div.tutor-ml-auto.tutor-align-center.tutor-d-none.tutor-d-xl-flex > div.tutor-topbar-complete-btn.tutor-mr-20 > form > button {
        margin: 0px 5px !important;
        display: flex !important;
        justify-content: flex-start !important; /* Soldan hizalama */
        align-items: center !important;
        min-width: auto !important;
        height: 36px !important;
        padding: 0 10px !important;
        font-size: 14px !important;
        border-color: var(--tutor-mobile-border) !important; /* Değişken kullanımı */
        color: var(--tutor-mobile-text) !important; /* Değişken kullanımı */
    }

    /* Tüm navigasyon butonları için ortak özellikler */
    .tutor-course-topic-single-header .tutor-topbar-mark-btn,
    #tutor-single-entry-content > div.tutor-course-topic-single-header.tutor-single-page-top-bar > div.tutor-ml-auto.tutor-align-center.tutor-d-none.tutor-d-xl-flex > div.tutor-topbar-complete-btn.tutor-mr-20 > form > button {
        height: 36px !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        min-width: auto !important;
        transition: all 0.2s ease !important; /* Geçiş efekti */
    }
}

@media (max-width: 1199px) {
    /**
     * Önceki ve Sonraki Butonlar
     * İçerikler arasında gezinmek için kullanılan butonlar.
     * Mobil görünümde sadece ikon olarak gösterilir.
     */
    .tutor-course-topic-single-header .tutor-single-course-content-prev .tutor-btn,
    .tutor-course-topic-single-header .tutor-single-course-content-next .tutor-btn {
        width: 36px !important;
        height: 36px !important;
        padding: 0 !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        min-width: auto !important;
        border-color: var(--tutor-mobile-border) !important; /* Değişken kullanımı */
        background-color: transparent !important;
    }

    /**
     * Buton İkonları Hizalama
     * Butonlar içindeki ikonların düzgün görüntülenmesi.
     * Tüm ikonlar için tutarlı bir görünüm sağlar.
     */
    .tutor-course-topic-single-header .tutor-topbar-mark-btn i,
    .tutor-course-topic-single-header .tutor-topbar-mark-btn [class^="tutor-icon-"],
    #tutor-single-entry-content > div.tutor-course-topic-single-header.tutor-single-page-top-bar > div.tutor-ml-auto.tutor-align-center.tutor-d-none.tutor-d-xl-flex > div.tutor-topbar-complete-btn.tutor-mr-20 > form > button i,
    #tutor-single-entry-content > div.tutor-course-topic-single-header.tutor-single-page-top-bar > div.tutor-ml-auto.tutor-align-center.tutor-d-none.tutor-d-xl-flex > div.tutor-topbar-complete-btn.tutor-mr-20 > form > button [class^="tutor-icon-"] {
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        margin: 0 auto !important;
        color: var(--tutor-mobile-text) !important; /* Değişken kullanımı */
    }

    /**
     * Buton Metinlerini Gizleme
     * Mobil görünümde buton metinlerinin gizlenmesi.
     * Daha kompakt bir görünüm sağlar.
     */
    .tutor-course-topic-single-header .tutor-single-course-content-prev .tutor-btn .tutor-ml-8,
    .tutor-course-topic-single-header .tutor-single-course-content-next .tutor-btn .tutor-mr-8 {
        display: none !important;
    }

    /**
     * Gereksiz Elemanları Gizleme
     * Mobil görünümde ihtiyaç duyulmayan elemanların gizlenmesi.
     * Daha temiz bir kullanıcı arayüzü sağlar.
     */

    /* İkinci çarpı ikonunu gizle */
    .tutor-course-topic-single-header + div > .tutor-iconic-btn {
        display: none !important;
    }

    /* Sayfanın genişliğini ayarla (çift çarpı sorunu için) */
    #tutor-single-entry-content > div:nth-child(2) {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
        opacity: 0 !important;
        visibility: hidden !important;
    }

    /* İlerleme çubuğu ve Mark as Complete butonunu mobil görünümde gizle */
    .tutor-spotlight-mobile-progress-complete {
        display: none !important;
    }

    /**
     * Video İçeriği Görünürlüğü
     * Video içeriğinin düzgün görüntülenmesi için gerekli ayarlar.
     * İçeriğin tam olarak görünmesini sağlar.
     */
    #tutor-single-entry-content .tutor-single-page-top-bar + div {
        display: block !important;
        width: 100% !important;
        height: auto !important;
        position: relative !important;
        visibility: visible !important;
        opacity: 1 !important;
        overflow: visible !important;
    }
}

@media (max-width: 1199px) {
    /**
     * Mobil Görünümde İçerik Metni İçin Sınırlamalar
     * Küçük ekranlarda metin içeriğinin düzgün görüntülenmesi.
     * Kenar boşlukları ve kelime sarma özellikleri ile okunabilirliği artırır.
     */
    .tutor-course-single-content-wrapper .tutor-container,
    .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-mt-44,
    .tutor-course-single-content-wrapper #tutor-single-entry-content h3,
    .tutor-course-single-content-wrapper #tutor-single-entry-content p {
        padding-left: 15px !important;
        padding-right: 15px !important;
        box-sizing: border-box !important;
        max-width: 100% !important;
        word-wrap: break-word !important;
    }

    /**
     * Ders Hakkında Bölümü Metinleri
     * Ders açıklamasının görüntülendiği alandaki metin stilleri.
     * Alt menü ile çakışmaması için alt boşluk eklenir.
     */
    #tutor-single-entry-content .tutor-container h3,
    #tutor-single-entry-content .tutor-container p {
        padding-left: 15px !important;
        padding-right: 15px !important;
        word-break: break-word !important;
        padding-bottom: 50px !important; /* Alt menü ile çakışmaması için */
    }
}


/**
 * Tamamlama Butonu Stilleri - Masaüstü
 * İçeriği tamamlandı olarak işaretlemek için kullanılan buton.
 * Kullanıcının ilerleme durumunu güncellemesini sağlar.
 */
html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-topbar-mark-btn {
    color: var(--tutor-color-primary) !important; /* Değişken kullanımı */
    background-color: color-mix(in srgb, var(--tutor-color-primary) 10%, transparent) !important; /* Hafif mavi arka plan */
    border-color: transparent !important;
    transition: all 0.3s ease !important; /* Geçiş efekti */
}

/**
 * Tamamlandı Butonu İçin Özel Stil
 * İçerik tamamlandığında butonun görünümü.
 * Kullanıcıya görsel geri bildirim sağlar.
 */
html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-btn-success.tutor-topbar-mark-btn {
    color: #0e8500 !important; /* Yeşil metin */
    background-color: #1194011c !important; /* Hafif yeşil arka plan */
    border-color: transparent !important;
}

/**
 * Tamamlama Butonu Stilleri - Mobil/Tablet
 * Küçük ekranlarda tamamlama butonunun görünümü.
 * Mobil arayüze uygun tasarım.
 */
@media (max-width: 1199px) {
    html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-topbar-mark-btn {
        color: var(--tutor-mobile-text) !important; /* Değişken kullanımı */
        background-color: transparent !important; /* Saydam arka plan */
        border-color: var(--tutor-mobile-border) !important; /* Değişken kullanımı */
    }

    /**
     * Mobil Görünümde Tamamlandı Butonu
     * Mobil ekranlarda tamamlandı durumundaki buton görünümü.
     * Tutarlı bir kullanıcı arayüzü sağlar.
     */
    html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-btn-success.tutor-topbar-mark-btn {
        color: var(--tutor-mobile-text) !important; /* Değişken kullanımı */
        background-color: transparent !important; /* Saydam arka plan */
        border-color: var(--tutor-mobile-border) !important; /* Değişken kullanımı */
    }

    /**
     * Buton İçi Metin ve İkon Düzeni
     * Buton içindeki metin ve ikonların hizalanması.
     * Düzgün bir görünüm sağlar.
     */
    #tutor-single-entry-content > div.tutor-course-topic-single-header.tutor-single-page-top-bar > div.tutor-ml-auto.tutor-align-center.tutor-d-none.tutor-d-xl-flex > div.tutor-topbar-complete-btn.tutor-mr-20 > button > span.completion-btn-text,
    .tutor-course-topic-single-header .tutor-topbar-mark-btn span:not([class^="tutor-icon-"]) {
        padding: 1px 0px 0px 5px !important; /* Metin için iç boşluk */
    }

    /* Tamamlama butonu ikonu için padding */
    .tutor-course-topic-single-header .tutor-topbar-mark-btn i,
    .tutor-course-topic-single-header .tutor-topbar-mark-btn [class^="tutor-icon-"] {
        padding-right: 0 !important;
        margin-right: 0 !important;
    }

    /**
     * Gereksiz Butonları Gizleme
     * Mobil görünümde ihtiyaç duyulmayan butonların gizlenmesi.
     * Daha temiz bir kullanıcı arayüzü sağlar.
     */
    #tutor-single-entry-content > div.tutor-course-topic-single-header.tutor-single-page-top-bar > div.tutor-ml-auto.tutor-align-center.tutor-d-none.tutor-d-xl-flex > div.tutor-topbar-complete-btn.tutor-mr-20 {
        margin-right: 0 !important;
        display: none !important;
    }
}

@media (max-width: 1199px) {
    /**
     * Mobil Görünümde Hamburger Menü Yanındaki Tamamlama Butonu
     * Alt menüdeki hamburger butonu yanında yer alan tamamlama butonu.
     * Kullanıcının içeriği kolayca tamamlamasını sağlar.
     */
    .tutor-mobile-complete-btn {
        margin-left: 10px !important; /* Sol kenar boşluğu */
    }

    .tutor-mobile-complete-btn .tutor-topbar-complete-btn {
        margin-right: 0 !important; /* Sağ kenar boşluğu kaldırıldı */
    }

    /**
     * Mobil Tamamlama Butonu Stilleri
     * Hamburger menü yanındaki tamamlama butonunun görünümü.
     * Mobil arayüze uygun tasarım.
     */
    .tutor-mobile-complete-btn .tutor-topbar-mark-btn {
        height: 36px !important; /* Sabit yükseklik */
        padding: 0 10px !important; /* Yatay iç boşluk */
        font-size: 14px !important; /* Yazı boyutu */
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        min-width: auto !important;
        border-radius: 4px !important;
        background-color: transparent !important; /* Saydam arka plan */
        color: var(--tutor-mobile-text) !important; /* Değişken kullanımı */
        border-color: var(--tutor-mobile-border) !important; /* Değişken kullanımı */
        transition: all 0.2s ease !important; /* Geçiş efekti */
    }

    /**
     * Tamamlandı Butonu İçin Özel Stil
     * İçerik tamamlandığında butonun görünümü.
     * Kullanıcıya görsel geri bildirim sağlar.
     */
    .tutor-mobile-complete-btn .tutor-topbar-mark-btn.tutor-btn-success {
        color: var(--tutor-mobile-text) !important; /* Değişken kullanımı */
        background-color: transparent !important; /* Saydam arka plan */
        border-color: var(--tutor-mobile-border) !important; /* Değişken kullanımı */
    }
}

/* Masaüstü görünümünde mobil tamamlama butonunu gizle */
@media (min-width: 1200px) {
    .tutor-mobile-complete-btn {
        display: none !important;
    }
}

/**
 * Hover Efektleri - Sadece Masaüstü İçin
 * Fare ile üzerine gelindiğinde butonların görünümü.
 * Kullanıcıya tıklanabilir öğeleri belirtir ve görsel geri bildirim sağlar.
 */
@media (min-width: 1200px) {
    /**
     * Tamamlama Butonu Hover Efekti
     * Fare ile üzerine gelindiğinde tamamlama butonunun görünümü.
     * Kullanıcıya tıklanabilir olduğunu belirtir.
     */
    html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-topbar-mark-btn:hover {
        color: #ffffff !important; /* Beyaz metin */
        background-color: var(--tutor-success-color) !important; /* Değişken kullanımı */
        transform: translateY(-2px) !important; /* Hafif yukarı hareket */
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important; /* Hafif gölge efekti */
    }

    /**
     * Tamamlandı Butonu Hover Durumu
     * Fare ile üzerine gelindiğinde tamamlandı butonunun görünümü.
     * Kullanıcıya tıklanabilir olduğunu belirtir.
     */
    html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-btn-success.tutor-topbar-mark-btn:hover {
        color: #ffffff !important; /* Beyaz metin */
        background-color: var(--tutor-success-hover) !important; /* Değişken kullanımı */
        transform: translateY(-2px) !important; /* Hafif yukarı hareket */
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important; /* Hafif gölge efekti */
    }
}

/**
 * İkincil Buton Stilleri
 * Alternatif işlemler ve ikincil öncelikli butonlar için.
 * Ana butondan sonra ikinci derecede önemli eylemleri temsil eder.
 */
.tutor-btn-secondary {
    color: var(--tutor-color-primary) !important; /* Mavi metin */
    background-color: var(--tutor-medium-primary) !important; /* Hafif mavi arka plan */
    transition: all 0.3s ease !important; /* Geçiş efekti */
}

/**
 * İkincil Buton Mobil/Tablet Stilleri
 * Küçük ekranlarda ikincil butonların görünümü.
 * Mobil arayüze uygun tasarım.
 */
@media (max-width: 1199px) {
    .tutor-btn-secondary {
        color: var(--tutor-mobile-text) !important; /* Değişken kullanımı */
        background-color: transparent !important; /* Saydam arka plan */
        border-color: var(--tutor-mobile-border) !important; /* Değişken kullanımı */
    }
}

/**
 * İkincil Buton Hover Efekti
 * Fare ile üzerine gelindiğinde ikincil butonun görünümü.
 * Kullanıcıya tıklanabilir olduğunu belirtir.
 */
.tutor-btn-secondary:hover {
    background-color: var(--tutor-color-primary) !important; /* Tam mavi arka plan */
    color: #ffffff !important; /* Beyaz metin */
    transform: translateY(-2px) !important; /* Hafif yukarı hareket */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important; /* Hafif gölge efekti */
}

/**
 * İkon Butonları Stilleri
 * Sadece ikon içeren butonların görünümü ve davranışları.
 * Kompakt ve anlaşılır bir kullanıcı arayüzü sağlar.
 */

/**
 * Temel İkon Butonu
 * Standart ikon butonu görünümü.
 * Tüm ikon butonları için temel stil.
 */
html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-iconic-btn {
    color: var(--tutor-color-primary) !important; /* Mavi ikon rengi */
    transition: all 0.3s ease !important; /* Geçiş efekti */
}

/**
 * İkincil İkon Butonu
 * Alternatif işlemler için kullanılan ikon butonu.
 * Saydam arka plana sahiptir.
 */
html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-iconic-btn-secondary {
    background-color: rgba(0, 0, 0, 0) !important; /* Saydam arka plan */
}

/**
 * Hamburger Menü İkonu
 * Yan menüyü açmak/kapatmak için kullanılan ikon.
 * Kullanıcıya menü işlevini görsel olarak belirtir.
 */
html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-icon-hamburger-menu {
    font-size: 18px !important; /* İkon boyutu */
    color: var(--tutor-color-primary) !important; /* Mavi ikon rengi */
}

/**
 * Masaüstü İçin Hover Efektleri
 * Fare ile üzerine gelindiğinde ikon butonlarının görünümü.
 * Kullanıcıya tıklanabilir öğeleri belirtir.
 */
@media (min-width: 1200px) {
    /* Hamburger menü ikonu hover efekti */
    html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-icon-hamburger-menu:hover,
    html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-iconic-btn-secondary:hover .tutor-icon-hamburger-menu {
        color: var(--tutor-color-primary) !important; /* Mavi ikon rengi korunur */
        transform: scale(1.1) !important; /* Hafif büyütme efekti */
    }

    /* İkincil ikon butonu hover efekti */
    html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-iconic-btn-secondary:hover {
        background-color: #ffffff !important; /* Beyaz arka plan */
    }

    /* Genel ikon butonu hover efekti */
    html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-iconic-btn:hover {
        color: var(--tutor-color-primary) !important; /* Mavi ikon rengi */
        border-color: var(--tutor-color-primary) !important; /* Mavi kenarlık */
        transform: translateY(-2px) !important; /* Hafif yukarı hareket */
    }
}

/**
 * Mobil ve Tablet İçin İkon Buton Stilleri
 * Küçük ekranlarda ikon butonlarının görünümü.
 * Dokunmatik kullanıma uygun tasarım.
 */
@media (max-width: 1199px) {
    /* Mobil görünümde hover efektlerini kaldır */
    html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-iconic-btn:hover {
        background-color: transparent !important; /* Saydam arka plan */
        color: var(--tutor-mobile-text) !important; /* Değişken kullanımı */
        border-color: transparent !important; /* Kenarlık yok */
    }

    /* Hamburger menü butonu hover efekti */
    html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-iconic-btn-secondary:hover {
        background-color: transparent !important; /* Saydam arka plan */
    }

    /* Sidebar açıkken hamburger menü butonu */
    html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-iconic-btn.tutor-hamburger-active {
        background-color: rgba(255, 255, 255, 0.13) !important; /* Yarı saydam beyaz arka plan */
    }
}

/* Responsive Başlık Ayarları Yukarıya Taşındı */

/* Eski tutor-ml-xl-24 seçicisi tarafından belirlenen masaüstü stili güncellendi */
@media (min-width: 1200px) {
    html body .tutor-ml-xl-24 {
        margin-left: 10px !important;
        max-width: 700px !important; /* Başlık için maksimum genişlik */
        white-space: nowrap !important;
        overflow: hidden !important;
        position: relative !important;
        text-overflow: ellipsis !important;
    }

    /* Animasyon kaldırıldı, her zaman üç nokta (...) ile kesilecek */
    html body .tutor-ml-xl-24.tutor-marquee {
        text-overflow: ellipsis !important;
        animation: none !important;
        overflow: hidden !important;
        padding-right: 15px !important;
    }
}


/* ===============================================================
   6. VİDEO OYNATICI STİLLERİ
   =============================================================== */

/**
 * Video Oynatıcı Bölümü
 * Kurs içeriğindeki video oynatıcının görünümü ve davranışları.
 * Kullanıcıya kaliteli bir video izleme deneyimi sağlar.
 */

/**
 * Video Oynatıcı Dış Kapsayıcı
 * Video oynatıcının etrafındaki boşluk ve konumlandırma.
 * Sayfada düzgün yerleşim sağlar.
 */
.tutor-video-player-wrapper-modern,
.tutor-video-player-wrapper {
    padding: 20px 0;
    max-width: 100% !important; /* Taşmaları önlemek için */
}

/**
 * Ana Video Oynatıcı Konteyneri
 * Video oynatıcının ana kapsayıcısı.
 * Yuvarlatılmış köşeler ve kenar boşlukları ile görsel çekicilik sağlar.
 */
.tutor-video-player {
    border-radius: 12px;
    overflow: hidden;
    margin: 0 auto 30px;
    width: 90%;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1); /* Hafif gölge efekti */
}

/**
 * Video Konteyneri
 * Oynatıcının içindeki video alanı.
 * Yuvarlatılmış köşeler ile görsel uyum sağlar.
 */
.tutor-video-player .plyr__video-wrapper {
    border-radius: 10px;
}

/**
 * Oynat Butonu Kaplaması
 * Video üzerindeki büyük oynat butonu.
 * Kullanıcıya tıklanabilir alanı belirtir.
 */
.tutor-video-player .plyr__control--overlaid {
    background: var(--tutor-color-primary); /* Değişken kullanımı */
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5); /* Belirgin gölge */
}

/**
 * Oynat Butonu Hover Efekti
 * Fare ile üzerine gelindiğinde oynat butonunun görünümü.
 * Kullanıcıya tıklanabilir olduğunu belirtir.
 */
.tutor-video-player .plyr__control--overlaid:hover {
    transform: translate(-50%, -50%) scale(1.1); /* Hafif büyütme efekti */
}

/**
 * Video Kontrol Butonları Hover ve Focus Durumları
 * Kontrol butonlarının etkileşim durumlarındaki görünümü.
 * Kullanıcıya görsel geri bildirim sağlar.
 */
.tutor-video-player .plyr--video .plyr__control.plyr__tab-focus,
.tutor-video-player .plyr--video .plyr__control:hover,
.tutor-video-player .plyr--video .plyr__control[aria-expanded=true] {
    background: var(--tutor-color-primary) !important; /* Değişken kullanımı */
}

/**
 * İlerleme Çubuğu Rengi
 * Video ilerleme çubuğunun rengi.
 * Kullanıcıya görsel geri bildirim sağlar.
 */
.tutor-video-player .plyr--full-ui input[type=range] {
    color: var(--tutor-color-primary) !important; /* Değişken kullanımı */
}

/**
 * Duyarlı Videolar İçin Oran Konteyneri
 * Farklı ekran boyutlarında video oranını koruyan kapsayıcı.
 * Responsive tasarım için gereklidir.
 */
.tutor-video-player .tutor-ratio {
    position: relative;
    display: block;
    width: 100%;
    padding: 0;
    overflow: hidden;
}

/**
 * 16:9 Video Oranı
 * Standart video oranı için padding değeri.
 * Video içeriğinin doğru oranda görüntülenmesini sağlar.
 */
.tutor-video-player .tutor-ratio-16x9 {
    padding-top: 56.25%; /* 16:9 oranı */
}

/**
 * İframe ve Video Elemanları
 * Gömülü video ve iframe'lerin konumlandırılması.
 * İçeriğin doğru şekilde görüntülenmesini sağlar.
 */
.tutor-video-player .tutor-ratio iframe,
.tutor-video-player .tutor-ratio video {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0;
}


/* ===============================================================
   7. NAVİGASYON ÇUBUĞU STİLLERİ
   =============================================================== */

/**
 * Navigasyon Çubuğu Stilleri
 * Kurs öğrenme ekranındaki navigasyon çubuğunun görünümü.
 * Kenar boşlukları ve kenarlık stili özelleştirilmiştir.
 */

/* Web Görünümü İçin Navigasyon Çubuğu Stilleri */
@media (min-width: 768px) {
    .tutor-nav:not(.tutor-nav-pills):not(.tutor-nav-tabs) {
        margin: 0 !important; /* Kenar boşlukları kaldırıldı */
        border-bottom: 1px dashed var(--tutor-border-color) !important; /* Kesikli alt kenarlık */
    }
}

/* Sadece 1200px ve üzeri ekranlarda kurs izleme ekranında margin değerini uygula */
@media (min-width: 1200px) {
    body.tutor-course-viewing-page .tutor-nav:not(.tutor-nav-pills):not(.tutor-nav-tabs) {
        margin: 0px 60px 10px 60px !important; /* Kenar boşlukları: üst 0px, sağ 60px, alt 10px, sol 60px */
    }
}

/* Mobil Görünüm İçin Navigasyon Çubuğu Stilleri */
@media (max-width: 767px) {
    .tutor-nav:not(.tutor-nav-pills):not(.tutor-nav-tabs) {
        margin: 0 !important; /* Kenar boşluklarını kaldır */
        border-bottom: 1px dashed var(--tutor-border-color) !important; /* Kesikli alt kenarlık */
    }
}

/* ===============================================================
   8. SEKME VE TAB STİLLERİ
   =============================================================== */

/**
 * Sekme ve Tab Bölümü
 * İçeriğin farklı kategorilere ayrıldığı sekme sistemi.
 * Kullanıcının içerik türleri arasında kolayca geçiş yapmasını sağlar.
 */

/**
 * Sekme Butonları Temel Stilleri
 * Tüm ekran boyutları için geçerli olan temel sekme stilleri.
 * Tutarlı bir kullanıcı arayüzü sağlar.
 */
#tutor-single-entry-content > div.tutor-course-topic-single-body > div.tutor-course-spotlight-wrapper > ul li a,
.tutor-course-topic-single-body .tutor-course-spotlight-wrapper .tutor-nav-tabs li a {
    position: relative !important;
    transition: all 0.2s ease !important; /* Geçiş efekti */
}

/**
 * Sekme Altı Çizgi Efekti
 * Sekme butonlarının altında görünen çizgi.
 * Aktif sekmeyi görsel olarak belirtir.
 */
#tutor-single-entry-content > div.tutor-course-topic-single-body > div.tutor-course-spotlight-wrapper > ul li a::after,
.tutor-course-topic-single-body .tutor-course-spotlight-wrapper .tutor-nav-tabs li a::after {
    content: "" !important;
    position: absolute !important;
    left: 0 !important;
    bottom: 0 !important;
    width: 100% !important; /* Tam genişlik */
    height: 2px !important;
    background-color: transparent !important; /* Varsayılan olarak saydam */
    transition: background-color 0.3s !important;
}

/**
 * Aktif Sekme Çizgisi
 * Seçili olan sekmenin altındaki renkli çizgi.
 * Kullanıcıya hangi sekmenin aktif olduğunu belirtir.
 */
#tutor-single-entry-content > div.tutor-course-topic-single-body > div.tutor-course-spotlight-wrapper > ul li.active a::after,
.tutor-course-topic-single-body .tutor-course-spotlight-wrapper .tutor-nav-tabs li.active a::after {
    background-color: var(--tutor-color-primary) !important; /* Aktif tab için mavi çizgi */
}

/**
 * Gereksiz Başlıkları Gizleme
 * Sekme içeriklerindeki tekrarlanan başlıkların gizlenmesi.
 * Daha temiz bir kullanıcı arayüzü sağlar.
 */
/* Overview sekmesindeki başlığı gizle */
#tutor-course-spotlight-overview > div > div > div > div.tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-mb-12 {
    display: none !important;
}

/* Files sekmesindeki başlığı gizle */
#tutor-course-spotlight-files > div > div > div > div.tutor-fs-5.tutor-fw-medium.tutor-color-black {
    display: none !important;
}

/* Comments sekmesindeki başlığı gizle */
#tutor-course-spotlight-comments > div > div > div > div.tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-mb-36 {
    display: none !important;
}

/**
 * Sekme İçeriği İçin Boşluk Ayarları
 * Sekme içeriklerinin kenar boşlukları.
 * Daha iyi okunabilirlik ve görsel düzen sağlar.
 */
/* Tab içeriği için padding-top ayarla */
.tutor-course-spotlight-wrapper .tutor-course-spotlight-tab {
    padding-top: 10px !important;
}

/* Yorum kısmı için üstten padding ekleme */
#tutor-course-spotlight-comments > div {
    padding-top: 20px !important;
}

/**
 * Tablet ve Mobil İçin Sekme Butonları
 * Küçük ekranlarda sekmelerin yatay kaydırılabilir şekilde görüntülenmesi.
 * Tüm sekmelere erişimi kolaylaştırır.
 */
@media (max-width: 1199px) {
    #tutor-single-entry-content > div.tutor-course-topic-single-body > div.tutor-course-spotlight-wrapper > ul,
    .tutor-course-topic-single-body .tutor-course-spotlight-wrapper .tutor-nav-tabs {
        display: flex !important;
        flex-direction: row !important;
        flex-wrap: nowrap !important;
        overflow-x: auto !important; /* Gerekirse yatay kaydırma */
        justify-content: center !important; /* Ortadan hizalama */
        width: 100% !important;
        padding: 0 10px !important;
        scrollbar-width: none !important; /* Firefox için kaydırma çubuğunu gizle */
    }

    /**
     * Kaydırma Çubuğunu Gizleme
     * Sekme alanındaki kaydırma çubuğunun gizlenmesi.
     * Daha temiz bir görünüm sağlar.
     */
    #tutor-single-entry-content > div.tutor-course-topic-single-body > div.tutor-course-spotlight-wrapper > ul::-webkit-scrollbar,
    .tutor-course-topic-single-body .tutor-course-spotlight-wrapper .tutor-nav-tabs::-webkit-scrollbar {
        display: none !important;
    }

    /**
     * Sekme Öğeleri Stilleri
     * Her bir sekme öğesinin görünümü ve davranışı.
     * Kullanıcı dostu bir arayüz sağlar.
     */
    #tutor-single-entry-content > div.tutor-course-topic-single-body > div.tutor-course-spotlight-wrapper > ul li,
    .tutor-course-topic-single-body .tutor-course-spotlight-wrapper .tutor-nav-tabs li {
        flex: 0 0 auto !important; /* Esnek olmayan genişlik */
        margin-right: 15px !important; /* Sağ kenar boşluğu */
    }

    /* Son elemanın sağ kenar boşluğunu kaldır */
    #tutor-single-entry-content > div.tutor-course-topic-single-body > div.tutor-course-spotlight-wrapper > ul li:last-child,
    .tutor-course-topic-single-body .tutor-course-spotlight-wrapper .tutor-nav-tabs li:last-child {
        margin-right: 0 !important;
    }

    /**
     * Sekme Bağlantıları
     * Sekme öğelerinin içindeki bağlantı stilleri.
     * Tıklanabilir alanları belirginleştirir.
     */
    #tutor-single-entry-content > div.tutor-course-topic-single-body > div.tutor-course-spotlight-wrapper > ul li a,
    .tutor-course-topic-single-body .tutor-course-spotlight-wrapper .tutor-nav-tabs li a {
        font-size: 13px !important; /* Küçük yazı boyutu */
        padding: 10px 5px !important; /* Kompakt iç boşluk */
        white-space: nowrap !important; /* Metin sarma kapalı */
    }
}

/**
 * Mobil Görünümde Sekme Butonları
 * Çok küçük ekranlarda sekmelerin görünümü.
 * Dokunmatik kullanıma uygun tasarım.
 */
@media (max-width: 767px) {
    #tutor-single-entry-content > div.tutor-course-topic-single-body > div.tutor-course-spotlight-wrapper > ul li a,
    .tutor-course-topic-single-body .tutor-course-spotlight-wrapper .tutor-nav-tabs li a {
        font-size: 12px !important; /* Daha küçük yazı boyutu */
        padding: 8px 5px !important; /* Daha kompakt iç boşluk */
    }
}

/* Sidebar top padding and progress bar */
html body .tutor-course-single-sidebar-wrapper {
    padding-top: 0px !important;
}

/* Add this class to body via JavaScript */
html body.desktop-progress-added .tutor-spotlight-mobile-progress-left {
    width: 100% !important;
    flex: 0 0 100% !important;
}

/* Sidebar Kurs Başlığı */
html body .desktop-course-title {
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #212327 !important;
    margin-bottom: 12px !important;
    padding-bottom: 0px !important;
    word-break: break-word !important;
    overflow-wrap: break-word !important;
    hyphens: auto !important;
}

@media (max-width: 767px) {
    html body .desktop-course-title {
        margin-right: 30px !important;
    }
}

/* Mobil Kurs Başlığı */
html body .mobile-course-title {
    padding-left: 10px !important;
    max-width: 90% !important;
    display: block !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    font-weight: 600 !important;
    line-height: 1.4 !important;
    margin-bottom: 5px !important;
    white-space: normal !important;
}

@media (max-width: 767px) {
    html body .mobile-course-title {
        max-width: 55% !important;
        border-right: 2px solid #f44336 !important;
        padding-right: 10px !important;
        margin-right: 40px !important;
    }
}

/* İlerleme çubuğu stilleri */
.tutor-progress-bar {
    height: 9px !important;
    background: #dfe7ff !important;
    will-change: transform !important; /* Hardware acceleration */
    transform: translateZ(0) !important;
    transition: none !important;
}


/* ===============================================================
   8. ANİMASYON VE EFEKTLER
   =============================================================== */

/* İlerleme Çubuğu Güncelleme Animasyonu - Kaldırıldı */
.tutor-progress-bar.tutor-progress-updated {
    animation: none !important;
}

/* Navigasyon butonları için güncelleme animasyonu - Kaldırıldı */
.tutor-single-course-content-prev a.tutor-updating,
.tutor-single-course-content-next a.tutor-updating {
    position: relative !important;
    pointer-events: none !important;
    opacity: 1 !important;
}

.tutor-single-course-content-prev a.tutor-updating::after,
.tutor-single-course-content-next a.tutor-updating::after {
    content: '' !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    width: 16px !important;
    height: 16px !important;
    margin-top: -8px !important;
    margin-left: -8px !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    border-top-color: #ffffff !important;
    border-radius: 50% !important;
    animation: spin 1s linear infinite !important;
}

/* Sidebar linkleri için yükleme animasyonu */
.tutor-course-topic-item a.tutor-sidebar-loading {
    position: relative !important;
    pointer-events: none !important;
    opacity: 0.7 !important;
    background-color: rgba(var(--tutor-primary-rgb), 0.05) !important;
}

.tutor-course-topic-item a.tutor-sidebar-loading::after {
    content: '' !important;
    position: absolute !important;
    top: 50% !important;
    right: 15px !important;
    width: 12px !important;
    height: 12px !important;
    margin-top: -6px !important;
    border: 2px solid rgba(var(--tutor-primary-rgb), 0.2) !important;
    border-top-color: var(--tutor-color-primary) !important;
    border-radius: 50% !important;
    animation: spin 1s linear infinite !important;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Topic tamamlanma sayıları için animasyon */
.tutor-course-topic-summary.tutor-topic-updated {
    animation: topic-update-pulse 0.5s ease-in-out;
}

@keyframes topic-update-pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.8; color: var(--tutor-color-primary); }
    100% { transform: scale(1); opacity: 1; }
}

/* Checkbox görünürlük sınıfı */
.tutor-form-check-input.checked-visible {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Form kontrol elementi için border opaklığı */
.tutor-form-control {
    border-color: rgba(0, 0, 0, 0) !important;
}

/* Optimize sidebar images with better loading */
.tutor-course-topic-item-thumbnail {
    width: 65px !important;
    height: 45px !important;
    border-radius: 4px !important;
    object-fit: cover !important;
    margin-right: 8px !important;
    vertical-align: middle !important;
    /* loading: lazy is added via JavaScript instead */
}

/* Optimize gradient placeholder with reduced complexity */
.tutor-course-topic-item-gradient {
    width: 65px !important;
    height: 45px !important;
    border-radius: 4px !important;
    margin-right: 8px !important;
    background: var(--tutor-color-primary) !important; /* Değişken kullanımı */
    display: inline-block !important;
    position: relative !important;
}

/* Simplify gradient icon with optimized SVG */
.tutor-course-topic-item-gradient::after {
    content: '';
    position: absolute !important;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 24px !important;
    height: 24px !important;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgba(255,255,255,0.9)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='18' height='18' rx='2'%3E%3C/rect%3E%3Ccircle cx='8.5' cy='8.5' r='1.5'%3E%3C/circle%3E%3Cpolyline points='21 15 16 10 5 21'%3E%3C/polyline%3E%3C/svg%3E") !important;
    background-size: contain !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
}

/* Optimize Text Truncation */
.tutor-ml-xl-24.tutor-marquee {
    text-overflow: ellipsis !important;
    animation: none !important;
    overflow: hidden !important;
    white-space: nowrap !important;
    display: inline-block !important;
}


/* ===============================================================
   9. RESPONSIVE TASARIM AYARLARI
   =============================================================== */

/* Optimize media queries by combining similar rules */
@media (max-width: 767px) {
    .tutor-course-topic-item-title {
        max-width: 150px !important;
    }
}

@media (min-width: 768px) and (max-width: 1199px) {
    .tutor-course-topic-item-title {
        max-width: 300px !important;
    }
}

@media (min-width: 1200px) {
    .tutor-course-topic-item-title {
        max-width: 350px !important; /* Sidebar genişliği arttığı için başlık genişliği de arttırıldı */
    }
}

/* Yan sütun arka plan rengi */
.tutor-course-single-content-wrapper.tutor-course-single-sidebar-open .tutor-course-single-sidebar-wrapper {
    background-color: #f5f7fd !important;
    border-right: none !important; /* Sağ kenarlığı kaldır */
}

/* Mobil progress bar konumlandırma */
@media (max-width: 1199px) {
    html body .desktop-progress-bar {
        display: block !important;
        padding: 0 20px !important; /* Sağa taşıma için padding eklendi */
        margin-bottom: 15px !important;
    }

    html body .mobile-course-title {
        padding-left: 10px !important;
    }

    html body.mobile-progress-added .tutor-spotlight-mobile-progress-left {
        width: 100% !important;
        flex: 0 0 100% !important;
        padding-left: 10px !important;
    }
}

/* Sidebar kapatma butonu pozisyonu */
a[tutor-hide-course-single-sidebar] {
    position: absolute !important;
    top: -100px !important;
    right: 15px !important;
    z-index: 999 !important;
}

/* Çarpı ikonu stilleri (eski seçiciler korundu) */
.tutor-course-single-sidebar-wrapper .tutor-icon-times {
    position: relative !important;
}



.tutor-form-control-wrapper .tutor-icon-times {
    position: absolute !important;
    top: 10px !important;
    right: 10px !important;
    z-index: 999 !important;
}

/* Tutor Course Topic Single Footer Özellik Kaldırma */
.tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-footer {
    border-top: none !important;
    background-color: transparent !important;
}

/* Mobil görünümde sağdaki fazla çarpı ikonunu gizle */
a.tutor-iconic-btn.tutor-d-flex.tutor-d-xl-none.tutor-ml-auto {
    display: none !important;
}

/* Sol kenar çubuğundaki kurs içerik başlıklarını sınırlandırma - yukarıda optimize edildi */

/* Responsive Başlık Ayarları Yukarıya Taşındı */

/* Aktif içerik öğesi için stil ayarları yukarıda tanımlandı */

/* Hide video title in the header */
#tutor-single-entry-content > div.tutor-course-topic-single-header.tutor-single-page-top-bar > div.tutor-course-topic-single-header-title.tutor-fs-6.tutor-ml-12.tutor-ml-xl-24 {
    display: none !important;
}

/* Remove desktop progress bar in sidebar */
#tutor-page-wrap > div.tutor-course-single-content-wrapper.tutor-spotlight-mode > div.tutor-course-single-sidebar-wrapper.tutor-lesson-sidebar > div.desktop-progress-bar {
    display: none !important;
}

/* Sidebar padding ekle */
#tutor-page-wrap > div.tutor-course-single-content-wrapper.tutor-spotlight-mode > div.tutor-course-single-sidebar-wrapper.tutor-lesson-sidebar {
    padding: 0px 0px 100px 10px !important;
}


/* ===============================================================
   10. PERFORMANS OPTİMİZASYONLARI
   =============================================================== */

/* Sidebar Optimization - Performance Improvements */
/* Hardware acceleration for sidebar animations */
.tutor-course-single-sidebar-wrapper {
    will-change: transform !important;
    transform: translateZ(0) !important;
    backface-visibility: hidden !important;
    perspective: 1000px !important;
    overflow-x: hidden !important; /* Yatay kaydırma çubuğunu gizle - tüm ekran boyutları için */
}

/* Fixed Sidebar for Desktop */
@media (min-width: 1200px) {
    .tutor-course-single-sidebar-wrapper {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        height: 100vh !important;
        overflow-y: auto !important;
        overflow-x: hidden !important; /* Yatay kaydırma çubuğunu gizle */
        overscroll-behavior: contain !important; /* Kaydırma hareketlerinin dışarıya taşmasını engelle */
        scrollbar-gutter: stable !important; /* Kaydırma çubuğu için yer ayır, böylece içerik sıçramaz */
        z-index: 99 !important;
        width: 420px !important; /* Sidebar genişliği - 400px olarak ayarlandı */
        padding-top: 0 !important;
        border-right: none !important; /* Sağ kenarlığı kaldır */
    }

    /* İçerik alanı için sol margin ekleyerek sidebar'ın kapladığı alanı dengeleme */
    .tutor-course-single-content-wrapper #tutor-single-entry-content {
        margin-left: 400px !important; /* Sidebar genişliği kadar sol margin */
        width: calc(100% - 400px) !important; /* Toplam genişlikten sidebar genişliğini çıkar */
    }

    /* Sidebar kapalıyken içerik alanı için daha az sol margin */
    body.tutor-sidebar-expanded .tutor-course-single-content-wrapper #tutor-single-entry-content {
        margin-left: 200px !important; /* Sidebar kapalıyken daha az sol margin */
        width: calc(100% - 400px) !important; /* Genişlik her zaman aynı kalsın */
    }

    /* Sidebar kapalıyken sidebar'ı tamamen gizle */
    body.tutor-sidebar-expanded .tutor-course-single-sidebar-wrapper {
        display: none !important;
    }

    /* Sidebar açıkken sidebar'ı göster */
    body:not(.tutor-sidebar-expanded) .tutor-course-single-sidebar-wrapper {
        display: block !important;
    }

    /* Sidebar içindeki elementlerin genişliğini ayarla */
    .tutor-course-single-sidebar-wrapper .tutor-accordion {
        width: 400px !important;
    }

    /* Sidebar başlığı için genişlik sınırlaması kaldırıldı */
    #tutor-page-wrap > div.tutor-course-single-content-wrapper.tutor-spotlight-mode > div.tutor-course-single-sidebar-wrapper.tutor-lesson-sidebar > div.tutor-course-single-sidebar-title.tutor-d-flex.tutor-justify-between {
        width: auto !important;
    }

    /* Sidebar scrollbar stilini düzenle - başlangıçta şeffaf olsun */
    .tutor-course-single-sidebar-wrapper::-webkit-scrollbar {
        width: 5px !important;
        background-color: transparent !important;
        display: block !important; /* Her zaman göster */
    }

    .tutor-course-single-sidebar-wrapper::-webkit-scrollbar-thumb {
        background-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0) !important; /* Başlangıçta şeffaf kaydırma çubuğu */
        border-radius: 10px !important; /* Kaydırma çubuğu köşe yuvarlaklığı */
        transition: background-color 0.2s ease !important; /* Geçiş efekti */
    }

    /* Hover durumunda scrollbar'ı görünür yap */
    .tutor-course-single-sidebar-wrapper:hover::-webkit-scrollbar-thumb {
        background-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0.3) !important; /* Hover durumunda görünür kaydırma çubuğu */
    }

    /* Kaydırma çubuğunun üzerine gelindiğinde daha belirgin yap */
    .tutor-course-single-sidebar-wrapper::-webkit-scrollbar-thumb:hover {
        background-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 1) !important; /* Kaydırma çubuğu hover durumunda tam opaklık */
    }

    /* Firefox için scrollbar stilini düzenle - başlangıçta şeffaf olsun */
    .tutor-course-single-sidebar-wrapper {
        scrollbar-width: thin !important;
        scrollbar-color: transparent transparent !important; /* Firefox için şeffaf kaydırma çubuğu */
        -ms-overflow-style: auto !important; /* IE ve Edge için */
    }

    /* Firefox için hover durumunda scrollbar'ı görünür yap */
    .tutor-course-single-sidebar-wrapper:hover {
        scrollbar-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0.3) transparent !important; /* Firefox için görünür kaydırma çubuğu */
    }
}

/* Optimize accordion animations */
.tutor-course-single-sidebar-wrapper .tutor-accordion-item-header {
    transition: none !important; /* Remove transition for better performance */
}

/* Optimize accordion item body animations */
.tutor-course-single-sidebar-wrapper .tutor-accordion-item-body {
    transition: none !important; /* Remove transition for better performance */
    will-change: transform, opacity !important;
    transform: translateZ(0) !important;
}

/* Reduce repaints when opening/closing topics */
.tutor-course-single-sidebar-wrapper .tutor-course-topic {
    will-change: transform !important;
    transform: translateZ(0) !important;
    backface-visibility: hidden !important;
}

/* Optimize course topic items */
.tutor-course-single-sidebar-wrapper .tutor-course-topic-item {
    will-change: transform !important;
    transform: translateZ(0) !important;
    transition: none !important; /* Remove transition for better performance */
}

/* Reduce layout shifts */
.tutor-course-single-sidebar-wrapper .tutor-accordion {
    contain: content !important;
}

/* Optimize images in sidebar */
.tutor-course-single-sidebar-wrapper img {
    contain: paint !important;
}

/* Prevent FOUC (Flash of Unstyled Content) */
.tutor-course-single-sidebar-wrapper .tutor-accordion-item-body-content {
    contain: paint !important;
}

/* Tablet ve mobil cihazlar için video player optimizasyonu */
@media (max-width: 1199px) {
    .tutor-video-player-wrapper-modern {
        padding: 0 !important;
    }

    .tutor-video-player {
        width: 100% !important;
        border-radius: 0 !important;
    }

    /* Video player içindeki iframe'ler için de stil */
    .tutor-video-player iframe {
        width: 100% !important;
        border-radius: 0 !important;
    }

    /* Hamburger menu iconu (sol tarafta) - renk beyaz */
    html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-icon-hamburger-menu {
        font-size: 16px !important;
        color: #ffffff !important;
    }

    /* Mobil hamburger menü butonu stili */
    .tutor-mobile-hamburger-btn {
        width: 36px !important;
        height: 36px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        background-color: transparent !important;
        border-color: #ffffff52 !important;
        border-radius: 4px !important;
    }

    /* Hamburger menü butonu aktif durumu */
    .tutor-mobile-hamburger-btn.tutor-hamburger-active {
        background-color: #ffffff21 !important;
    }

    /* Çarpı ikonu (sağ tarafta) - renk beyaz */
    html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-ml-auto .tutor-icon-times {
        font-size: 16px !important;
        color: #ffffff !important;
    }

    .tutor-video-player .plyr__video-wrapper {
        border-radius: 0px !important;
    }

    /* Iconic Button hover durumu için arka plan rengi - kaldırıldı */
    /*
    html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header .tutor-iconic-btn:hover {
        background-color: #ffffff24 !important;
        color: #ffffff !important;
        border-color: transparent !important;
    }
    */

    /* İçerik başlıkları ve paragraflar için alt boşluk */
    #tutor-single-entry-content .tutor-container h3,
    #tutor-single-entry-content .tutor-container p {
        padding-bottom: 50px !important;
    }
}

/* İlk ve son dersler için navigasyon butonlarını gizleme */
/* İlk ders için önceki butonu gizle */
.tutor-single-course-content-prev a[disabled="disabled"] {
    display: none !important;
}

/* Son ders için sonraki butonu gizle */
.tutor-single-course-content-next a[disabled="disabled"] {
    display: none !important;
}

/* Butonun kapsayıcı div'ini de gizle (eğer içinde başka bir şey yoksa) */
.tutor-single-course-content-prev:has(a[disabled="disabled"]) {
    display: none !important;
}

.tutor-single-course-content-next:has(a[disabled="disabled"]) {
    display: none !important;
}

/* Alternatif çözüm - modern tarayıcılar için :has desteği yoksa */
.tutor-single-course-content-prev a[disabled="disabled"] + .tutor-single-course-content-prev,
.tutor-single-course-content-next a[disabled="disabled"] + .tutor-single-course-content-next {
    display: none !important;
}

/* Mobil ve tablet görünümünde sidebar pozisyonu */
@media (max-width: 1199px) {
    .tutor-course-single-sidebar-wrapper {
        flex: 0 0 100%;
        width: 100%;
        position: fixed;
        top: 0px;
        opacity: 0;
        z-index: -1;
        overflow-x: hidden !important; /* Yatay kaydırma çubuğunu gizle */
        overflow-y: auto !important; /* Dikey kaydırma ekle */
        padding: 0px 10px !important; /* Mobil görünümde de padding ekle */
        pointer-events: none; /* Varsayılan olarak tıklanamaz yap */
        border-right: none !important; /* Sağ kenarlığı kaldır */

        /* Kaydırma çubuğu stilini ayarla - başlangıçta şeffaf olsun */
        scrollbar-width: thin !important; /* Firefox için ince kaydırma çubuğu */
        scrollbar-color: transparent transparent !important; /* Firefox için başlangıçta şeffaf kaydırma çubuğu */
        -ms-overflow-style: auto !important; /* IE ve Edge için */
    }

    /* Webkit (Chrome, Safari) için kaydırma çubuğu stilini ayarla */
    .tutor-course-single-sidebar-wrapper::-webkit-scrollbar {
        width: 5px !important; /* Kaydırma çubuğu genişliği */
        background-color: transparent !important; /* Kaydırma çubuğu arka plan rengi */
        display: block !important; /* Her zaman göster */
    }

    .tutor-course-single-sidebar-wrapper::-webkit-scrollbar-thumb {
        background-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0) !important; /* Başlangıçta şeffaf kaydırma çubuğu */
        border-radius: 10px !important; /* Kaydırma çubuğu köşe yuvarlaklığı */
        transition: background-color 0.2s ease !important; /* Geçiş efekti */
    }

    /* Hover durumunda scrollbar'ı görünür yap */
    .tutor-course-single-sidebar-wrapper:hover::-webkit-scrollbar-thumb {
        background-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0.3) !important; /* Hover durumunda görünür kaydırma çubuğu */
    }

    /* Kaydırma çubuğunun üzerine gelindiğinde daha belirgin yap */
    .tutor-course-single-sidebar-wrapper::-webkit-scrollbar-thumb:hover {
        background-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 1) !important; /* Kaydırma çubuğu hover durumunda tam opaklık */
    }

    /* Firefox için hover durumunda scrollbar'ı görünür yap */
    .tutor-course-single-sidebar-wrapper:hover {
        scrollbar-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0.3) transparent !important; /* Firefox için görünür kaydırma çubuğu */
    }

    /* Sidebar açıldığında göster ve tıklanabilir yap */
    .tutor-course-single-sidebar-wrapper.tutor-lesson-sidebar-show {
        opacity: 1 !important;
        z-index: 999 !important;
        pointer-events: auto !important; /* Tıklanabilir yap */
    }

    /* Mobil görünümde sidebar padding */
    #tutor-page-wrap > div.tutor-course-single-content-wrapper.tutor-spotlight-mode > div.tutor-course-single-sidebar-wrapper.tutor-lesson-sidebar {
        padding: 0px 10px 144px 10px !important;
    }

    /* Sidebar overlay - mobil/tablet görünümünde sidebar açıldığında arka planı karartmak için */
    .tutor-sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 998; /* Sidebar'dan bir düşük z-index */
        display: none;
    }

    /* Sidebar açıldığında overlay'i göster */
    .tutor-sidebar-overlay.tutor-sidebar-overlay-show {
        display: block;
    }
}


/* ===============================================================
   11. YORUMLAR MODÜLÜ STİLLERİ
   =============================================================== */

/**
 * Tutor LMS Comments Custom CSS
 * Yorumlar bölümü için özel stil tanımlamaları
 */

/* Cevapları ve cevap formunu varsayılan olarak gizle */
.tutor-comments-list .tutor-child-comment,
.tutor-single-comment .tutor-child-comment,
.tutor-comments-list form[tutor-comment-reply],
.tutor-course-spotlight-comments .tutor-child-comment,
.tutor-reply-box,
.tutor-comment-line {
    display: none !important;
}

/* Yorum metni için maksimum genişlik ve kelime sarma */
.tutor-comment-text {
    max-width: 560px !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    white-space: normal !important;
}

/* Ekran boyutu 1024px ile 760px arasında olduğunda max-width ayarı */
@media (min-width: 760px) and (max-width: 1024px) {
    .tutor-comment-text {
        max-width: 560px !important;
    }
}

/* Ekran boyutu 700px'den küçük olduğunda max-width ayarı */
@media (max-width: 700px) {
    .tutor-comment-text {
        max-width: calc(100vw - 140px) !important; /* Her piksel için max-width 1px azalır */
    }
}

/* Yorum container'ları için genişlik ayarı */
.tutor-comments-list,
.tutor-single-comment {
    width: 100% !important;
    max-width: 100% !important;
}

/* Cevap kutusu için padding ayarı */
.tutor-course-spotlight-wrapper .tutor-conversation .tutor-comments-list .tutor-single-comment .tutor-comment-box.tutor-reply-box {
    padding: 16px 0px 16px 0px !important;
}

/* Cevapla butonuna hover efekti ekle */
.tutor-comment-actions span {
    cursor: pointer;
    transition: color 0.2s ease;
}

.tutor-comment-actions span:hover {
    color: var(--tutor-color-primary) !important;
}

/* Cevap sayısı için stil */
.tutor-reply-count {
    display: inline-block;
    margin-left: 5px;
    color: inherit !important; /* Cevapla yazısı ile aynı renk */
    font-weight: 500;
}

/* Hover durumunda cevap sayısı da mavi olsun */
.tutor-comment-actions span:hover .tutor-reply-count {
    color: var(--tutor-color-primary) !important;
}

/* Aktif yorum için stil */
.tutor-comments-list.show-replies .tutor-child-comment,
.tutor-comments-list.show-replies .tutor-reply-box,
.tutor-comments-list.show-replies form[tutor-comment-reply],
.tutor-single-comment.show-replies .tutor-child-comment,
.tutor-single-comment.show-replies .tutor-reply-box,
.tutor-comments-list.show-replies .tutor-comment-line {
    display: block !important;
    animation: fadeIn 0.3s ease;
}

/* Yorum çizgileri için düzeltme */
.tutor-comments-list {
    position: relative;
}

/* Yorum çizgisi (dikey) */
.tutor-comment-line {
    position: absolute;
    left: 25px; /* Avatar'ın ortasına denk gelecek şekilde */
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #DCDFE5;
    z-index: 1;
}

/* İlk seviye yorumlar için çizgi */
.tutor-parent-comment > .tutor-comment-line {
    top: 50px; /* Avatar'ın altından başlasın */
    height: calc(100% - 50px);
}

/* Son yorum için çizgi düzeltmesi */
.tutor-comments-list:last-child .tutor-comment-line {
    height: 100%;
    top: 0;
}

/* Yorum avatar için stil */
.comment-avatar {
    position: relative;
    z-index: 2; /* Çizginin üzerinde görünsün */
}

.comment-avatar img {
    border: 3px solid #fff; /* Avatar etrafında beyaz çerçeve */
    background-color: #fff; /* Arka plan beyaz olsun */
}

/* Ana yorum yazma alanı için stil */
.tutor-comment-box:not(.tutor-reply-box) {
    align-items: flex-start;
}

.tutor-comment-box:not(.tutor-reply-box) .tutor-comment-textarea {
    flex: 1;
}

/* Animasyon kaldırıldı */


/* ===============================================================
   12. HAMBURGER MENÜ HOVER EFEKTİ
   =============================================================== */

/**
 * Hamburger Menü Hover Efekti
 * Yan menüyü açmak/kapatmak için kullanılan butonun etkileşim stilleri.
 * Kullanıcıya tıklanabilir olduğunu belirtir ve görsel geri bildirim sağlar.
 */

/* Web görünümünde hamburger menü butonu hover efekti */
@media (min-width: 1200px) {
    /**
     * Hamburger Menü Butonu Hover Durumu
     * Fare ile üzerine gelindiğinde butonun görünüm değişimi.
     * Kullanıcıya tıklanabilir olduğunu belirtir.
     */
    .tutor-iconic-btn.tutor-iconic-btn-secondary.tutor-d-none.tutor-d-xl-flex:hover {
        background-color: rgba(255, 255, 255, 0.2) !important; /* Yarı saydam beyaz arka plan */
        border-radius: 50% !important; /* Yuvarlak buton */
        transform: scale(1.1) !important; /* Hafif büyütme efekti */
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important; /* Hafif gölge efekti */
    }
}


/* ===============================================================
   13. FORM ELEMANLARI STİLLERİ
   =============================================================== */

/**
 * Form Elemanları Stilleri
 * Giriş alanları, onay kutuları ve diğer form elemanlarının görünümü.
 * Kullanıcı dostu bir form deneyimi sağlar.
 */

/**
 * Yuvarlak Checkbox Stil Düzenlemeleri
 * Onay kutularının görünümü ve hizalanması.
 * Daha iyi bir kullanıcı deneyimi için düzenlenmiştir.
 */
.tutor-course-single-content-wrapper input.tutor-form-check-input.tutor-form-check-circle {
    margin-top: 10px !important; /* Üst kenar boşluğu */
    cursor: pointer !important; /* Tıklanabilir imleç */
    transition: all 0.2s ease !important; /* Geçiş efekti */
}

/**
 * Kenar Boşluğu Düzenlemeleri
 * Web görünümünde bazı sınıflar için özel kenar boşluğu değerleri.
 * Daha dengeli bir sayfa düzeni sağlar.
 */
@media (min-width: 1200px) {
    .tutor-mr-20 {
        margin-right: 10px !important; /* Varsayılan 20px yerine 10px */
    }
}

/**
 * Sidebar Konu Öğeleri Hover ve Aktif Durumları
 * Fare ile üzerine gelindiğinde veya aktif olduğunda metin rengi.
 * Kullanıcıya görsel geri bildirim sağlar.
 */
.tutor-course-single-sidebar-wrapper .tutor-course-topic-item:hover .tutor-course-topic-item-icon,
.tutor-course-single-sidebar-wrapper .tutor-course-topic-item:hover .tutor-course-topic-item-title,
.tutor-course-single-sidebar-wrapper .tutor-course-topic-item.is-active .tutor-course-topic-item-icon,
.tutor-course-single-sidebar-wrapper .tutor-course-topic-item.is-active .tutor-course-topic-item-title {
    color: #3C4C5A !important;
}

/**
 * Soluk Metin Rengi
 * İkincil önem taşıyan metinler için kullanılan renk.
 * Kullanıcı arayüzünde hiyerarşi oluşturur.
 */
.tutor-color-muted {
    color: #3C4C5A !important;
}

/* ===============================================================
   KURS DETAY SAYFASI SORU-CEVAP YAZMA ALANI ÖZELLEŞTİRMESİ
   =============================================================== */

/**
 * Kurs detay sayfasındaki soru-cevap yazı yazma alanı için özel border rengi
 * Bu stil sadece kurs detay sayfasında (.tutor-course-details-page) uygulanır
 * ve diğer sayfalardaki form kontrollerini etkilemez.
 */
.tutor-course-details-page .tutor-form-control {
    border-color: rgb(0 0 0 / 24%) !important;
}
